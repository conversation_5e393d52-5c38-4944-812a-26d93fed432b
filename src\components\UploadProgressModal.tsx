import React from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import {UploadProgress, UploadItem, UploadStatus} from '../services/UploadService';

interface UploadProgressModalProps {
  visible: boolean;
  progress: UploadProgress | null;
  uploadItems: UploadItem[];
  onClose: () => void;
  onCancel: () => void;
  onRetry: () => void;
}

const UploadProgressModal: React.FC<UploadProgressModalProps> = ({
  visible,
  progress,
  uploadItems,
  onClose,
  onCancel,
  onRetry,
}) => {
  const getStatusColor = (status: UploadStatus): string => {
    switch (status) {
      case UploadStatus.SUCCESS:
        return '#4caf50';
      case UploadStatus.FAILED:
        return '#f44336';
      case UploadStatus.UPLOADING:
        return '#2196f3';
      case UploadStatus.CANCELLED:
        return '#ff9800';
      default:
        return '#9e9e9e';
    }
  };

  const getStatusText = (status: UploadStatus): string => {
    switch (status) {
      case UploadStatus.PENDING:
        return '等待中';
      case UploadStatus.UPLOADING:
        return '上传中';
      case UploadStatus.SUCCESS:
        return '成功';
      case UploadStatus.FAILED:
        return '失败';
      case UploadStatus.CANCELLED:
        return '已取消';
      default:
        return '未知';
    }
  };

  const handleCancel = () => {
    Alert.alert(
      '确认取消',
      '确定要取消上传吗？已上传的图片不会被删除。',
      [
        {text: '继续上传', style: 'cancel'},
        {text: '取消上传', onPress: onCancel, style: 'destructive'},
      ],
    );
  };

  const handleRetry = () => {
    const failedCount = uploadItems.filter(item => item.status === UploadStatus.FAILED).length;
    if (failedCount === 0) {
      Alert.alert('提示', '没有失败的上传项需要重试');
      return;
    }

    Alert.alert(
      '重试上传',
      `确定要重试 ${failedCount} 个失败的上传项吗？`,
      [
        {text: '取消', style: 'cancel'},
        {text: '重试', onPress: onRetry},
      ],
    );
  };

  if (!progress) return null;

  const isCompleted = progress.completedItems + progress.failedItems === progress.totalItems;
  const hasFailures = progress.failedItems > 0;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>上传进度</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressInfo}>
            <Text style={styles.progressText}>
              {progress.completedItems} / {progress.totalItems} 完成
            </Text>
            <Text style={styles.progressPercentage}>
              {progress.overallProgress}%
            </Text>
          </View>
          
          <View style={styles.progressBarContainer}>
            <View
              style={[
                styles.progressBar,
                {width: `${progress.overallProgress}%`},
              ]}
            />
          </View>

          {progress.failedItems > 0 && (
            <Text style={styles.failedText}>
              {progress.failedItems} 个上传失败
            </Text>
          )}
        </View>

        <ScrollView style={styles.itemsList}>
          {uploadItems.map((item, index) => (
            <View key={item.id} style={styles.uploadItem}>
              <View style={styles.itemInfo}>
                <Text style={styles.itemName} numberOfLines={1}>
                  {item.photo.filename || `图片 ${index + 1}`}
                </Text>
                <View style={styles.itemStatus}>
                  <View
                    style={[
                      styles.statusDot,
                      {backgroundColor: getStatusColor(item.status)},
                    ]}
                  />
                  <Text style={styles.statusText}>
                    {getStatusText(item.status)}
                  </Text>
                </View>
              </View>
              
              {item.status === UploadStatus.UPLOADING && (
                <View style={styles.itemProgressContainer}>
                  <View
                    style={[
                      styles.itemProgressBar,
                      {width: `${item.progress}%`},
                    ]}
                  />
                </View>
              )}
              
              {item.error && (
                <Text style={styles.errorText} numberOfLines={2}>
                  {item.error}
                </Text>
              )}
              
              {item.retryCount > 0 && (
                <Text style={styles.retryText}>
                  重试次数: {item.retryCount}
                </Text>
              )}
            </View>
          ))}
        </ScrollView>

        <View style={styles.actions}>
          {!isCompleted && (
            <TouchableOpacity
              style={[styles.actionButton, styles.cancelButton]}
              onPress={handleCancel}>
              <Text style={styles.cancelButtonText}>取消上传</Text>
            </TouchableOpacity>
          )}
          
          {isCompleted && hasFailures && (
            <TouchableOpacity
              style={[styles.actionButton, styles.retryButton]}
              onPress={handleRetry}>
              <Text style={styles.retryButtonText}>重试失败项</Text>
            </TouchableOpacity>
          )}
          
          {isCompleted && (
            <TouchableOpacity
              style={[styles.actionButton, styles.doneButton]}
              onPress={onClose}>
              <Text style={styles.doneButtonText}>完成</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666',
  },
  progressContainer: {
    padding: 16,
    backgroundColor: '#f9f9f9',
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 16,
    color: '#333',
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6200ee',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#6200ee',
  },
  failedText: {
    marginTop: 8,
    fontSize: 14,
    color: '#f44336',
  },
  itemsList: {
    flex: 1,
    padding: 16,
  },
  uploadItem: {
    backgroundColor: '#ffffff',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  itemInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  itemName: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    marginRight: 8,
  },
  itemStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
  },
  itemProgressContainer: {
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginTop: 4,
    overflow: 'hidden',
  },
  itemProgressBar: {
    height: '100%',
    backgroundColor: '#2196f3',
  },
  errorText: {
    fontSize: 12,
    color: '#f44336',
    marginTop: 4,
  },
  retryText: {
    fontSize: 12,
    color: '#ff9800',
    marginTop: 2,
  },
  actions: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f44336',
  },
  cancelButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
  retryButton: {
    backgroundColor: '#ff9800',
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
  doneButton: {
    backgroundColor: '#4caf50',
  },
  doneButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default UploadProgressModal;
