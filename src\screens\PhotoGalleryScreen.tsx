import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import PhotoService, {PhotoItem} from '../services/PhotoService';
import PermissionService from '../services/PermissionService';
import UploadService, {UploadProgress, UploadItem} from '../services/UploadService';
import UploadProgressModal from '../components/UploadProgressModal';
import ServerConfig from '../config/ServerConfig';

const {width: screenWidth} = Dimensions.get('window');
const numColumns = 3;
const imageSize = (screenWidth - 40) / numColumns;

const PhotoGalleryScreen: React.FC = () => {
  const [photos, setPhotos] = useState<PhotoItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [endCursor, setEndCursor] = useState<string | undefined>();

  // 上传相关状态
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [uploadItems, setUploadItems] = useState<UploadItem[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const checkPermissionAndLoadPhotos = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const permissionResult = await PermissionService.ensurePhotoPermission();
      setHasPermission(permissionResult.granted);

      if (permissionResult.granted) {
        // 自动获取相册照片
        const result = await PhotoService.autoSyncPhotos();
        if (result.error) {
          setError(result.error);
        } else {
          setPhotos(result.photos);
          setHasNextPage(result.hasNextPage);
          setEndCursor(result.endCursor);
        }
      } else {
        setError(permissionResult.message || '无法获取相册权限');
      }
    } catch (err) {
      console.error('加载照片失败:', err);
      setError('加载照片时发生错误');
    } finally {
      setLoading(false);
    }
  }, []);

  const loadMorePhotos = useCallback(async () => {
    if (!hasNextPage || loading) return;

    setLoading(true);
    try {
      const result = await PhotoService.getPhotos(endCursor, 20);
      if (result.error) {
        setError(result.error);
      } else {
        setPhotos(prev => [...prev, ...result.photos]);
        setHasNextPage(result.hasNextPage);
        setEndCursor(result.endCursor);
      }
    } catch (err) {
      console.error('加载更多照片失败:', err);
      setError('加载更多照片时发生错误');
    } finally {
      setLoading(false);
    }
  }, [endCursor, hasNextPage, loading]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    setPhotos([]);
    setEndCursor(undefined);
    setHasNextPage(true);
    await checkPermissionAndLoadPhotos();
    setRefreshing(false);
  }, [checkPermissionAndLoadPhotos]);

  useEffect(() => {
    checkPermissionAndLoadPhotos();
  }, [checkPermissionAndLoadPhotos]);

  const renderPhotoItem = ({item}: {item: PhotoItem}) => (
    <TouchableOpacity
      style={styles.photoContainer}
      onPress={() => {
        Alert.alert(
          '照片信息',
          `文件名: ${item.filename || '未知'}\n尺寸: ${item.width}x${item.height}\n时间: ${new Date(item.timestamp * 1000).toLocaleString()}`,
        );
      }}>
      <Image source={{uri: item.uri}} style={styles.photo} resizeMode="cover" />
    </TouchableOpacity>
  );

  const renderFooter = () => {
    if (!loading) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color="#6200ee" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  };

  if (!hasPermission && !loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>
          {error || '需要相册权限才能显示照片'}
        </Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={checkPermissionAndLoadPhotos}>
          <Text style={styles.retryButtonText}>重新授权</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (loading && photos.length === 0) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#6200ee" />
        <Text style={styles.loadingText}>正在自动获取相册...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>相册照片 ({photos.length})</Text>
        <TouchableOpacity
          style={styles.syncButton}
          onPress={onRefresh}
          disabled={refreshing}>
          <Text style={styles.syncButtonText}>
            {refreshing ? '同步中...' : '重新同步'}
          </Text>
        </TouchableOpacity>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      <FlatList
        data={photos}
        renderItem={renderPhotoItem}
        keyExtractor={(item, index) => `${item.uri}-${index}`}
        numColumns={numColumns}
        contentContainerStyle={styles.photoList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onEndReached={loadMorePhotos}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={
          !loading ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>没有找到照片</Text>
            </View>
          ) : null
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  syncButton: {
    backgroundColor: '#6200ee',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  syncButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 12,
    margin: 16,
    borderRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#6200ee',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 4,
    marginTop: 16,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
  photoList: {
    padding: 10,
  },
  photoContainer: {
    margin: 5,
    borderRadius: 4,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  photo: {
    width: imageSize,
    height: imageSize,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
  },
});

export default PhotoGalleryScreen;
