import {PhotoItem} from './PhotoService';
import ServerConfig from '../config/ServerConfig';

export enum UploadStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface UploadItem {
  id: string;
  photo: PhotoItem;
  status: UploadStatus;
  progress: number;
  error?: string;
  retryCount: number;
  uploadedAt?: Date;
  serverId?: string;
}

export interface UploadProgress {
  totalItems: number;
  completedItems: number;
  failedItems: number;
  currentItem?: UploadItem;
  overallProgress: number;
}

export interface UploadResult {
  success: boolean;
  uploadedItems: UploadItem[];
  failedItems: UploadItem[];
  totalItems: number;
}

class UploadService {
  private uploadQueue: UploadItem[] = [];
  private isUploading = false;
  private progressCallback?: (progress: UploadProgress) => void;
  private abortController?: AbortController;

  setProgressCallback(callback: (progress: UploadProgress) => void): void {
    this.progressCallback = callback;
  }

  private generateUploadId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private createUploadItem(photo: PhotoItem): UploadItem {
    return {
      id: this.generateUploadId(),
      photo,
      status: UploadStatus.PENDING,
      progress: 0,
      retryCount: 0,
    };
  }

  addPhotosToQueue(photos: PhotoItem[]): UploadItem[] {
    const uploadItems = photos.map(photo => this.createUploadItem(photo));
    this.uploadQueue.push(...uploadItems);
    return uploadItems;
  }

  async uploadPhotos(photos: PhotoItem[]): Promise<UploadResult> {
    if (this.isUploading) {
      throw new Error('上传正在进行中，请等待完成');
    }

    const uploadItems = this.addPhotosToQueue(photos);
    return this.processUploadQueue();
  }

  async processUploadQueue(): Promise<UploadResult> {
    if (this.uploadQueue.length === 0) {
      return {
        success: true,
        uploadedItems: [],
        failedItems: [],
        totalItems: 0,
      };
    }

    this.isUploading = true;
    this.abortController = new AbortController();

    const config = ServerConfig.getServerConfig();
    const uploadConfig = ServerConfig.getUploadConfig();
    
    const uploadedItems: UploadItem[] = [];
    const failedItems: UploadItem[] = [];
    const totalItems = this.uploadQueue.length;

    try {
      // 分批处理上传
      for (let i = 0; i < this.uploadQueue.length; i += config.batchSize) {
        const batch = this.uploadQueue.slice(i, i + config.batchSize);
        
        const batchPromises = batch.map(item => this.uploadSinglePhoto(item));
        const batchResults = await Promise.allSettled(batchPromises);

        batchResults.forEach((result, index) => {
          const item = batch[index];
          if (result.status === 'fulfilled' && result.value.success) {
            item.status = UploadStatus.SUCCESS;
            item.progress = 100;
            item.uploadedAt = new Date();
            item.serverId = result.value.serverId;
            uploadedItems.push(item);
          } else {
            item.status = UploadStatus.FAILED;
            item.error = result.status === 'rejected' ? result.reason.message : '上传失败';
            failedItems.push(item);
          }
        });

        // 更新进度
        this.updateProgress(uploadedItems.length, failedItems.length, totalItems);

        // 检查是否被取消
        if (this.abortController.signal.aborted) {
          break;
        }
      }
    } catch (error) {
      console.error('批量上传失败:', error);
    } finally {
      this.isUploading = false;
      this.uploadQueue = [];
    }

    return {
      success: failedItems.length === 0,
      uploadedItems,
      failedItems,
      totalItems,
    };
  }

  private async uploadSinglePhoto(item: UploadItem): Promise<{success: boolean; serverId?: string}> {
    const config = ServerConfig.getServerConfig();
    const uploadConfig = ServerConfig.getUploadConfig();

    item.status = UploadStatus.UPLOADING;
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        item.retryCount = attempt;
        
        const formData = new FormData();
        
        // 添加图片文件
        const fileUri = item.photo.uri;
        const fileName = item.photo.filename || `photo_${Date.now()}.jpg`;
        
        formData.append(uploadConfig.fieldName, {
          uri: fileUri,
          type: 'image/jpeg',
          name: fileName,
        } as any);

        // 添加额外字段
        if (uploadConfig.additionalFields) {
          Object.entries(uploadConfig.additionalFields).forEach(([key, value]) => {
            formData.append(key, value);
          });
        }

        // 添加图片元数据
        formData.append('metadata', JSON.stringify({
          width: item.photo.width,
          height: item.photo.height,
          timestamp: item.photo.timestamp,
          originalName: item.photo.filename,
        }));

        const response = await fetch(ServerConfig.getFullUploadUrl(), {
          method: 'POST',
          body: formData,
          headers: {
            ...config.headers,
            'Content-Type': 'multipart/form-data',
          },
          signal: this.abortController?.signal,
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        
        return {
          success: true,
          serverId: result.id || result.photoId || result.fileId,
        };

      } catch (error: any) {
        console.error(`上传失败 (尝试 ${attempt + 1}/${config.maxRetries + 1}):`, error);
        
        if (attempt === config.maxRetries) {
          throw error;
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
      }
    }

    throw new Error('达到最大重试次数');
  }

  private updateProgress(completed: number, failed: number, total: number): void {
    if (this.progressCallback) {
      const progress: UploadProgress = {
        totalItems: total,
        completedItems: completed,
        failedItems: failed,
        overallProgress: Math.round(((completed + failed) / total) * 100),
      };
      this.progressCallback(progress);
    }
  }

  cancelUpload(): void {
    if (this.abortController) {
      this.abortController.abort();
    }
    
    this.uploadQueue.forEach(item => {
      if (item.status === UploadStatus.PENDING || item.status === UploadStatus.UPLOADING) {
        item.status = UploadStatus.CANCELLED;
      }
    });
    
    this.isUploading = false;
  }

  getUploadQueue(): UploadItem[] {
    return [...this.uploadQueue];
  }

  clearQueue(): void {
    this.uploadQueue = [];
  }

  isUploadInProgress(): boolean {
    return this.isUploading;
  }

  // 重试失败的上传
  async retryFailedUploads(): Promise<UploadResult> {
    const failedItems = this.uploadQueue.filter(item => item.status === UploadStatus.FAILED);
    
    if (failedItems.length === 0) {
      return {
        success: true,
        uploadedItems: [],
        failedItems: [],
        totalItems: 0,
      };
    }

    // 重置失败项状态
    failedItems.forEach(item => {
      item.status = UploadStatus.PENDING;
      item.progress = 0;
      item.retryCount = 0;
      item.error = undefined;
    });

    this.uploadQueue = failedItems;
    return this.processUploadQueue();
  }
}

export default new UploadService();
